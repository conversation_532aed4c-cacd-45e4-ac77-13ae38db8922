using EdgeGateway.Base.Entity.Dto.Permission;
using EdgeGateway.Base.Entity.Entity;
using EdgeGateway.Base.Entity.Enums;
using EdgeGateway.Core.Attribute;
using Furion.DynamicApiController;
using Furion.SpecificationDocument;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Mapster;
using Furion.FriendlyException;

namespace EdgeGateway.Base.Service.Permission;

/// <summary>
/// 系统权限服务
/// </summary>
[ApiDescriptionSettings(Order = 603)]
[Route("/api/sysPermission")]
[ModulePermission("PERMISSION_MANAGEMENT", "权限管理")]
public class SysPermissionService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<SysMenu> _sysMenuRep;
    private readonly SqlSugarRepository<SysRole> _sysRoleRep;
    private readonly SqlSugarRepository<SysRoleMenu> _sysRoleMenuRep;
    private readonly SqlSugarRepository<SysUserRole> _sysUserRoleRep;

    public SysPermissionService(
        SqlSugarRepository<SysMenu> sysMenuRep,
        SqlSugarRepository<SysRole> sysRoleRep,
        SqlSugarRepository<SysRoleMenu> sysRoleMenuRep,
        SqlSugarRepository<SysUserRole> sysUserRoleRep)
    {
        _sysMenuRep = sysMenuRep;
        _sysRoleRep = sysRoleRep;
        _sysRoleMenuRep = sysRoleMenuRep;
        _sysUserRoleRep = sysUserRoleRep;
    }

    /// <summary>
    /// 获取权限树
    /// </summary>
    /// <param name="name">权限名称</param>
    /// <param name="code">权限代码</param>
    /// <param name="type">权限类型</param>
    /// <param name="parentId">父级ID</param>
    /// <param name="enabled">是否启用</param>
    /// <returns>权限树</returns>
    [DisplayName("获取权限树")]
    [HttpGet("tree")]
    public async Task<List<PermissionTreeOutput>> GetPermissionTree(
        [FromQuery] string? name = null,
        [FromQuery] string? code = null,
        [FromQuery] string? type = null,
        [FromQuery] long? parentId = null,
        [FromQuery] bool? enabled = null)
    {
        var query = _sysMenuRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(name), m => m.Name.Contains(name!))
            .WhereIF(!string.IsNullOrWhiteSpace(code), m => m.Code.Contains(code!))
            .WhereIF(!string.IsNullOrWhiteSpace(type), m => m.MenuType.ToString().ToLower() == type!.ToLower())
            .WhereIF(parentId.HasValue, m => m.ParentId == parentId!.Value)
            .WhereIF(enabled.HasValue, m => m.Status == enabled!.Value)
            .OrderBy(m => m.Sort);

        var menus = await query.ToListAsync();

        return BuildPermissionTree(menus, null);
    }

    /// <summary>
    /// 分页获取权限列表
    /// </summary>
    /// <param name="input">查询参数</param>
    /// <returns>权限分页列表</returns>
    [DisplayName("分页获取权限列表")]
    [HttpGet("page")]
    public async Task<SqlSugarPagedList<PermissionOutput>> GetPermissionPage([FromQuery] PermissionPageInput input)
    {
        var query = _sysMenuRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), m => m.Name.Contains(input.Name!))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Code), m => m.Code.Contains(input.Code!))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Type), m => m.MenuType.ToString().ToLower() == input.Type!.ToLower())
            .WhereIF(input.ParentId.HasValue, m => m.ParentId == input.ParentId!.Value)
            .WhereIF(input.Enabled.HasValue, m => m.Status == input.Enabled!.Value)
            .OrderBy(m => m.Sort);

        var result = await query.ToPagedListAsync(input.Page, input.PageSize);

        return result.Adapt<SqlSugarPagedList<PermissionOutput>>();
    }

    /// <summary>
    /// 根据ID获取权限详情
    /// </summary>
    /// <param name="id">权限ID</param>
    /// <returns>权限详情</returns>
    [DisplayName("获取权限详情")]
    [HttpGet("{id}")]
    public async Task<PermissionOutput> GetPermissionById([Required] long id)
    {
        var menu = await _sysMenuRep.GetByIdAsync(id);
        if (menu == null)
            throw Oops.Oh("权限不存在");

        return new PermissionOutput
        {
            Id = menu.Id,
            Name = menu.Name,
            Code = menu.Code,
            Type = menu.MenuType.ToString().ToLower(),
            Description = null, // SysMenu没有Description属性
            ParentId = menu.ParentId,
            Enabled = menu.Status,
            Visible = !menu.Hidden, // Hidden转换为Visible
            Sort = menu.Sort,
            CreateTime = menu.CreateTime,
            UpdateTime = menu.UpdateTime
        };
    }

    /// <summary>
    /// 创建权限
    /// </summary>
    /// <param name="input">权限创建参数</param>
    /// <returns>权限ID</returns>
    [DisplayName("创建权限")]
    [HttpPost]
    public async Task<long> CreatePermission([Required] PermissionCreateInput input)
    {
        // 检查权限编码是否已存在
        var existMenu = await _sysMenuRep.AsQueryable()
            .FirstAsync(m => m.Code == input.Code);
        if (existMenu != null)
            throw Oops.Oh("权限编码已存在");

        // 检查父权限是否存在
        if (input.ParentId.HasValue)
        {
            var parentMenu = await _sysMenuRep.GetByIdAsync(input.ParentId.Value);
            if (parentMenu == null)
                throw Oops.Oh("父权限不存在");
        }

        var menu = new SysMenu
        {
            Name = input.Name,
            Code = input.Code,
            MenuType = ParseMenuType(input.Type),
            ParentId = input.ParentId,
            Status = input.Enabled ?? true,
            Hidden = !(input.Visible ?? true), // Visible转换为Hidden
            Sort = input.Sort ?? 0,
            CreateTime = DateTime.Now,
            UpdateTime = DateTime.Now
        };

        var result = await _sysMenuRep.InsertReturnSnowflakeIdAsync(menu);
        return result;
    }

    /// <summary>
    /// 更新权限
    /// </summary>
    /// <param name="id">权限ID</param>
    /// <param name="input">权限更新参数</param>
    [DisplayName("更新权限")]
    [HttpPut("{id}")]
    public async Task UpdatePermission([Required] long id, [Required] PermissionUpdateInput input)
    {
        var menu = await _sysMenuRep.GetByIdAsync(id);
        if (menu == null)
            throw Oops.Oh("权限不存在");

        // 检查权限编码是否已存在（排除自己）
        var existMenu = await _sysMenuRep.AsQueryable()
            .FirstAsync(m => m.Code == input.Code && m.Id != id);
        if (existMenu != null)
            throw Oops.Oh("权限编码已存在");

        // 检查父权限是否存在
        if (input.ParentId.HasValue)
        {
            var parentMenu = await _sysMenuRep.GetByIdAsync(input.ParentId.Value);
            if (parentMenu == null)
                throw Oops.Oh("父权限不存在");

            // 不能将自己设为父权限
            if (input.ParentId.Value == id)
                throw Oops.Oh("不能将自己设为父权限");
        }

        menu.Name = input.Name;
        menu.Code = input.Code;
        menu.MenuType = ParseMenuType(input.Type);
        menu.ParentId = input.ParentId;
        menu.Status = input.Enabled ?? true;
        menu.Hidden = !(input.Visible ?? true); // Visible转换为Hidden
        menu.Sort = input.Sort ?? 0;
        menu.UpdateTime = DateTime.Now;

        await _sysMenuRep.UpdateAsync(menu);
    }

    /// <summary>
    /// 删除权限
    /// </summary>
    /// <param name="id">权限ID</param>
    [DisplayName("删除权限")]
    [HttpDelete("{id}")]
    public async Task DeletePermission([Required] long id)
    {
        var menu = await _sysMenuRep.GetByIdAsync(id);
        if (menu == null)
            throw Oops.Oh("权限不存在");

        // 检查是否有子权限
        var hasChildren = await _sysMenuRep.AsQueryable()
            .AnyAsync(m => m.ParentId == id);
        if (hasChildren)
            throw Oops.Oh("存在子权限，无法删除");

        // 检查是否有角色在使用此权限
        var hasRoleMenu = await _sysRoleMenuRep.AsQueryable()
            .AnyAsync(rm => rm.MenuId == id);
        if (hasRoleMenu)
            throw Oops.Oh("权限正在被角色使用，无法删除");

        await _sysMenuRep.DeleteByIdAsync(id);
    }

    /// <summary>
    /// 获取权限模板列表
    /// </summary>
    /// <returns>权限模板列表</returns>
    [DisplayName("获取权限模板列表")]
    [HttpGet("templates")]
    public async Task<List<PermissionTemplateOutput>> GetPermissionTemplates()
    {
        // 基于角色创建权限模板
        var roles = await _sysRoleRep.AsQueryable()
            .Where(r => r.Status)
            .OrderBy(r => r.Sort)
            .ToListAsync();

        var templates = new List<PermissionTemplateOutput>();

        foreach (var role in roles)
        {
            // 获取角色的权限
            var roleMenus = await _sysRoleMenuRep.AsQueryable()
                .Where(rm => rm.RoleId == role.Id)
                .Select(rm => rm.MenuId)
                .ToListAsync();

            templates.Add(new PermissionTemplateOutput
            {
                Id = role.Id,
                Name = role.Name,
                Description = role.Description ?? $"{role.Name}权限模板",
                Permissions = roleMenus,
                Type = role.AccountType == AccountTypeEnum.SuperAdmin ? "system" : "custom",
                CreateTime = role.CreateTime,
                UpdateTime = role.UpdateTime
            });
        }

        return templates;
    }

    /// <summary>
    /// 创建权限模板
    /// </summary>
    /// <param name="input">权限模板创建参数</param>
    /// <returns>模板ID</returns>
    [DisplayName("创建权限模板")]
    [HttpPost("templates")]
    public async Task<long> CreatePermissionTemplate([Required] PermissionTemplateCreateInput input)
    {
        // 检查模板名称是否已存在
        var existRole = await _sysRoleRep.AsQueryable()
            .FirstAsync(r => r.Name == input.Name);
        if (existRole != null)
            throw Oops.Oh("权限模板名称已存在");

        // 创建对应的角色作为权限模板
        var role = new SysRole
        {
            Name = input.Name,
            Code = $"TEMPLATE_{input.Name.ToUpper()}",
            Description = input.Description,
            Status = true,
            AccountType = input.Type == "system" ? AccountTypeEnum.SuperAdmin : AccountTypeEnum.NormalUser,
            Sort = 999,
            CreateTime = DateTime.Now,
            UpdateTime = DateTime.Now
        };

        var roleId = await _sysRoleRep.InsertReturnSnowflakeIdAsync(role);

        // 分配权限
        if (input.Permissions?.Any() == true)
        {
            var roleMenus = input.Permissions.Select(permissionId => new SysRoleMenu
            {
                RoleId = roleId,
                MenuId = permissionId,
                CreateTime = DateTime.Now
            }).ToList();

            await _sysRoleMenuRep.InsertRangeAsync(roleMenus);
        }

        return roleId;
    }

    /// <summary>
    /// 更新权限模板
    /// </summary>
    /// <param name="id">模板ID</param>
    /// <param name="input">权限模板更新参数</param>
    [DisplayName("更新权限模板")]
    [HttpPut("templates/{id}")]
    public async Task<bool> UpdatePermissionTemplate([Required] long id, [Required] PermissionTemplateUpdateInput input)
    {
        var role = await _sysRoleRep.GetByIdAsync(id);
        if (role == null)
            throw Oops.Oh("权限模板不存在");

        // 检查模板名称是否已存在（排除自己）
        var existRole = await _sysRoleRep.AsQueryable()
            .FirstAsync(r => r.Name == input.Name && r.Id != id);
        if (existRole != null)
            throw Oops.Oh("权限模板名称已存在");

        // 更新角色信息
        role.Name = input.Name;
        role.Description = input.Description;
        role.AccountType = input.Type == "system" ? AccountTypeEnum.SuperAdmin : AccountTypeEnum.NormalUser;
        role.UpdateTime = DateTime.Now;

        await _sysRoleRep.UpdateAsync(role);

        // 删除原有的权限分配
        await _sysRoleMenuRep.DeleteAsync(rm => rm.RoleId == id);

        // 重新分配权限
        if (input.Permissions?.Any() == true)
        {
            var roleMenus = input.Permissions.Select(permissionId => new SysRoleMenu
            {
                RoleId = id,
                MenuId = permissionId,
                CreateTime = DateTime.Now
            }).ToList();

            await _sysRoleMenuRep.InsertRangeAsync(roleMenus);
        }

        return true;
    }

    /// <summary>
    /// 删除权限模板
    /// </summary>
    /// <param name="id">模板ID</param>
    [DisplayName("删除权限模板")]
    [HttpDelete("templates/{id}")]
    public async Task<bool> DeletePermissionTemplate([Required] long id)
    {
        var role = await _sysRoleRep.GetByIdAsync(id);
        if (role == null)
            throw Oops.Oh("权限模板不存在");

        // 检查是否有用户在使用此模板（角色）
        var hasUsers = await _sysUserRoleRep.AsQueryable()
            .AnyAsync(ur => ur.RoleId == id);
        if (hasUsers)
            throw Oops.Oh("权限模板正在被用户使用，无法删除");

        // 删除角色菜单关联
        await _sysRoleMenuRep.DeleteAsync(rm => rm.RoleId == id);

        // 删除角色
        await _sysRoleRep.DeleteByIdAsync(id);

        return true;
    }

    /// <summary>
    /// 构建权限树
    /// </summary>
    /// <param name="menus">菜单列表</param>
    /// <param name="parentId">父级ID</param>
    /// <returns>权限树</returns>
    private List<PermissionTreeOutput> BuildPermissionTree(List<SysMenu> menus, long? parentId)
    {
        var result = new List<PermissionTreeOutput>();

        var children = menus.Where(m => m.ParentId == parentId).ToList();
        foreach (var menu in children)
        {
            var node = new PermissionTreeOutput
            {
                Id = menu.Id,
                Name = menu.Name,
                Code = menu.Code,
                Type = menu.MenuType.ToString().ToLower(),
                Description = null, // SysMenu没有Description属性
                ParentId = menu.ParentId,
                Enabled = menu.Status,
                Visible = !menu.Hidden, // Hidden转换为Visible
                Sort = menu.Sort,
                CreateTime = menu.CreateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                UpdateTime = menu.UpdateTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                Children = BuildPermissionTree(menus, menu.Id)
            };

            result.Add(node);
        }

        return result;
    }

    /// <summary>
    /// 解析菜单类型
    /// </summary>
    /// <param name="type">类型字符串</param>
    /// <returns>菜单类型枚举</returns>
    private MenuTypeEnum ParseMenuType(string type)
    {
        return type?.ToLower() switch
        {
            "module" => MenuTypeEnum.Module,
            "function" => MenuTypeEnum.Menu,
            "data" => MenuTypeEnum.Menu,
            "api" => MenuTypeEnum.Menu,
            _ => MenuTypeEnum.Menu
        };
    }
}
